{"name": "auth", "type": "registry:module", "dependencies": ["better-auth", "better-sqlite3"], "devDependencies": ["@types/better-sqlite3"], "registryDependencies": ["button", "input", "label", "card", "separator", "avatar", "badge", "tabs"], "files": [{"name": "auth.ts", "content": "import { betterAuth } from \"better-auth\";\nimport { getModuleConfig } from \"@/config\";\nimport Database from \"better-sqlite3\";\nimport path from \"path\";\n\nconst authConfig = getModuleConfig('auth');\nconst dbPath = path.join(process.cwd(), 'auth.db');\nconst database = new Database(dbPath);\n\nexport const auth = betterAuth({\n  database,\n  baseURL: process.env.BETTER_AUTH_URL || \"http://localhost:3000\",\n  secret: process.env.BETTER_AUTH_SECRET || \"your-secret-key\",\n  emailAndPassword: { enabled: true },\n  socialProviders: {\n    ...(authConfig?.providers?.includes('google') && {\n      google: {\n        clientId: process.env.GOOGLE_CLIENT_ID as string,\n        clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,\n      }\n    }),\n    ...(authConfig?.providers?.includes('github') && {\n      github: {\n        clientId: process.env.GITHUB_CLIENT_ID as string,\n        clientSecret: process.env.GITHUB_CLIENT_SECRET as string,\n      }\n    }),\n  },\n});\n\nexport type Session = typeof auth.$Infer.Session;\nexport type User = typeof auth.$Infer.User;", "type": "registry:lib", "target": "lib/auth.ts"}], "meta": {"description": "Complete authentication module with betterAuth", "tags": ["auth", "authentication", "<PERSON><PERSON><PERSON>"], "version": "1.0.0"}}