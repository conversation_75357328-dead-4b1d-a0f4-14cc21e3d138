{"name": "waitlist-component", "type": "registry:module", "dependencies": [], "devDependencies": [], "registryDependencies": [], "files": [{"name": "waitlist-form.tsx", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { useWaitlist } from \"@/hooks/use-waitlist\";\nimport { Loader2, CheckCircle, AlertCircle } from \"lucide-react\";\n\ninterface WaitlistFormProps {\n  title?: string;\n  description?: string;\n  placeholder?: string;\n  buttonText?: string;\n  className?: string;\n}\n\nexport function WaitlistForm({\n  title = \"Join the Waitlist\",\n  description = \"Be the first to know when we launch!\",\n  placeholder = \"Enter your email address\",\n  buttonText = \"Join Waitlist\",\n  className,\n}: WaitlistFormProps) {\n  const [email, setEmail] = useState(\"\");\n  const { subscribe, isLoading, error, success } = useWaitlist();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n    \n    await subscribe({ email });\n    if (success) {\n      setEmail(\"\");\n    }\n  };\n\n  if (success) {\n    return (\n      <Card className={className}>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex items-center justify-center space-x-2 text-green-600\">\n            <CheckCircle className=\"h-5 w-5\" />\n            <p className=\"text-sm font-medium\">Successfully joined the waitlist!</p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle>{title}</CardTitle>\n        <CardDescription>{description}</CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\">Email</Label>\n            <Input\n              id=\"email\"\n              type=\"email\"\n              placeholder={placeholder}\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              required\n              disabled={isLoading}\n            />\n          </div>\n          \n          {error && (\n            <div className=\"flex items-center space-x-2 text-red-600\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <p className=\"text-sm\">{error}</p>\n            </div>\n          )}\n          \n          <Button type=\"submit\" disabled={isLoading || !email} className=\"w-full\">\n            {isLoading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n            {buttonText}\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}", "type": "registry:ui", "target": "components/ui/waitlist-form.tsx"}, {"name": "use-waitlist.ts", "content": "\"use client\";\n\nimport { useState } from \"react\";\nimport { getModuleConfig } from \"@/config\";\nimport { onWaitlistSignup } from \"@/actions/waitlist-actions\";\n\ninterface UseWaitlistOptions {\n  onSuccess?: (data: any) => void;\n  onError?: (error: string) => void;\n}\n\ninterface WaitlistSubscribeParams {\n  email: string;\n  name?: string;\n  referralCode?: string;\n}\n\nexport function useWaitlist(options: UseWaitlistOptions = {}) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState(false);\n  const [data, setData] = useState<any>(null);\n\n  const config = getModuleConfig('waitlist');\n\n  const subscribe = async (params: WaitlistSubscribeParams) => {\n    setIsLoading(true);\n    setError(null);\n    setSuccess(false);\n\n    try {\n      const response = await fetch('/api/waitlist', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(params),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Failed to join waitlist');\n      }\n\n      setData(result.data);\n      setSuccess(true);\n      \n      // Call server action for custom logic\n      await onWaitlistSignup({\n        email: params.email,\n        name: params.name,\n        referralCode: params.referralCode,\n        timestamp: new Date(),\n      });\n      \n      options.onSuccess?.(result.data);\n      \n      // Redirect if configured\n      if (config?.successRedirect) {\n        window.location.href = config.successRedirect;\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'An error occurred';\n      setError(errorMessage);\n      options.onError?.(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const reset = () => {\n    setError(null);\n    setSuccess(false);\n    setData(null);\n  };\n\n  return {\n    subscribe,\n    reset,\n    isLoading,\n    error,\n    success,\n    data,\n  };\n}", "type": "registry:hook", "target": "hooks/use-waitlist.ts"}, {"name": "waitlist-actions.ts", "content": "\"use server\";\n\nimport { WaitlistEntry } from \"@/types\";\n\n// ============================================================================\n// WAITLIST SERVER ACTIONS\n// ============================================================================\n\n/**\n * Called when a user signs up for the waitlist\n * Customize this function with your own business logic\n */\nexport async function onWaitlistSignup(params: {\n  email: string;\n  name?: string;\n  referralCode?: string;\n  timestamp: Date;\n}) {\n  // Add your custom logic here\n  console.log('New waitlist signup:', params);\n  \n  // Examples of what you might want to do:\n  // - Send welcome email\n  // - Track analytics event\n  // - Add to CRM\n  // - Send notification to team\n  // - Update user's referral count\n  \n  // Example: Log the signup\n  console.log(`New waitlist signup: ${params.email}`);\n  \n  // Example: You could send an email here\n  // await sendWelcomeEmail(params.email, params.name);\n  \n  // Example: Track analytics\n  // await trackEvent('waitlist_signup', { email: params.email });\n}\n\n/**\n * Called when a waitlist entry is approved\n */\nexport async function onWaitlistApproval(params: {\n  entry: WaitlistEntry;\n  approvedBy?: string;\n  timestamp: Date;\n}) {\n  console.log('Waitlist entry approved:', params.entry.email);\n  \n  // Add your custom logic here:\n  // - Send approval email\n  // - Create user account\n  // - Send onboarding sequence\n  // - Notify team\n}\n\n/**\n * Called when a waitlist entry is rejected\n */\nexport async function onWaitlistRejection(params: {\n  entry: WaitlistEntry;\n  rejectedBy?: string;\n  reason?: string;\n  timestamp: Date;\n}) {\n  console.log('Waitlist entry rejected:', params.entry.email);\n  \n  // Add your custom logic here:\n  // - Send rejection email (optional)\n  // - Log reason for analytics\n  // - Notify team\n}", "type": "registry:action", "target": "actions/waitlist-actions.ts"}, {"name": "route.ts", "content": "import { NextRequest, NextResponse } from \"next/server\";\nimport { getModuleConfig } from \"@/config\";\nimport { WaitlistEntry } from \"@/types\";\n\n// This is a simple in-memory store for demo purposes\n// In a real app, you'd use a database\nconst waitlistEntries: WaitlistEntry[] = [];\nlet nextId = 1;\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { email, name, referralCode } = body;\n\n    if (!email) {\n      return NextResponse.json(\n        { error: \"Email is required\" },\n        { status: 400 }\n      );\n    }\n\n    // Check if email already exists\n    const existingEntry = waitlistEntries.find(entry => entry.email === email);\n    if (existingEntry) {\n      return NextResponse.json(\n        { error: \"Email already registered\" },\n        { status: 409 }\n      );\n    }\n\n    // Get configuration\n    const config = getModuleConfig('waitlist');\n\n    // Create new waitlist entry\n    const entry: WaitlistEntry = {\n      id: nextId.toString(),\n      email,\n      name,\n      referralCode,\n      status: config?.autoApprove ? 'approved' : 'pending',\n      position: waitlistEntries.length + 1,\n      createdAt: new Date(),\n    };\n\n    if (config?.autoApprove) {\n      entry.approvedAt = new Date();\n    }\n\n    waitlistEntries.push(entry);\n    nextId++;\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        id: entry.id,\n        position: entry.position,\n        status: entry.status,\n      },\n    });\n  } catch (error) {\n    console.error('Waitlist API error:', error);\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const email = searchParams.get('email');\n\n    if (email) {\n      // Get specific entry\n      const entry = waitlistEntries.find(e => e.email === email);\n      if (!entry) {\n        return NextResponse.json(\n          { error: \"Entry not found\" },\n          { status: 404 }\n        );\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: {\n          id: entry.id,\n          position: entry.position,\n          status: entry.status,\n          createdAt: entry.createdAt,\n        },\n      });\n    }\n\n    // Get all entries (admin only - you'd add auth here)\n    return NextResponse.json({\n      success: true,\n      data: waitlistEntries.map(entry => ({\n        id: entry.id,\n        email: entry.email,\n        name: entry.name,\n        status: entry.status,\n        position: entry.position,\n        createdAt: entry.createdAt,\n        approvedAt: entry.approvedAt,\n      })),\n    });\n  } catch (error) {\n    console.error('Waitlist API error:', error);\n    return NextResponse.json(\n      { error: \"Internal server error\" },\n      { status: 500 }\n    );\n  }\n}", "type": "registry:api", "target": "app/api/waitlist/route.ts"}], "tailwind": {"config": {"theme": {"extend": {}}}}, "cssVars": {}, "meta": {"description": "Complete waitlist component with form, API, and server actions", "tags": ["waitlist", "email", "signup", "marketing"], "version": "1.0.0", "author": "Supreme Toolkit", "license": "MIT"}, "envVars": {"required": [], "optional": []}, "postInstall": {"instructions": ["Configure your waitlist settings in config.tsx", "Customize the server actions in actions/waitlist-actions.ts", "Add the WaitlistForm component to your pages", "Consider integrating with your email service for notifications"]}}