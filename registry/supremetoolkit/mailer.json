{
  "name": "mailer",
  "type": "registry:module",
  "dependencies": ["resend", "nodemailer"],
  "devDependencies": ["@types/nodemailer"],
  "registryDependencies": ["button", "input", "label", "card", "separator", "badge", "textarea"],
  "files": [
    {
      "name": "mailer.ts",
      "content": "/**\n * Supreme Toolkit - Unified Mailer\n * \n * This module automatically detects and uses the appropriate mailer\n * based on what's available in the user's environment.\n * \n * Priority:\n * 1. Resend (if RESEND_API_KEY is set)\n * 2. Nodemailer (if SMTP/Gmail config is set)\n * 3. Fallback to Resend with warning\n */\n\nexport interface EmailOptions {\n  to: string | string[];\n  from?: string;\n  subject: string;\n  html?: string;\n  text?: string;\n  replyTo?: string;\n  cc?: string | string[];\n  bcc?: string | string[];\n  attachments?: Array<{\n    filename: string;\n    content: string | Buffer;\n    contentType?: string;\n  }>;\n}\n\nexport interface MailerResult {\n  success: boolean;\n  id?: string;\n  error?: string;\n}\n\n/**\n * Detect which mailer to use based on environment variables\n */\nfunction detectMailer(): 'resend' | 'nodemailer' | 'none' {\n  // Check for Resend\n  if (process.env.RESEND_API_KEY) {\n    return 'resend';\n  }\n  \n  // Check for Nodemailer configurations\n  if (\n    process.env.SMTP_HOST || \n    process.env.GMAIL_USER || \n    process.env.EMAIL_PROVIDER\n  ) {\n    return 'nodemailer';\n  }\n  \n  return 'none';\n}\n\n/**\n * Get the appropriate mailer module\n */\nasync function getMailer() {\n  const mailerType = detectMailer();\n  \n  switch (mailerType) {\n    case 'resend':\n      try {\n        const resendMailer = await import('./mailer-resend');\n        return { type: 'resend', mailer: resendMailer.default };\n      } catch (error) {\n        console.warn('Resend mailer not available, falling back to nodemailer');\n        const nodemailerMailer = await import('./mailer-nodemailer');\n        return { type: 'nodemailer', mailer: nodemailerMailer.default };\n      }\n      \n    case 'nodemailer':\n      try {\n        const nodemailerMailer = await import('./mailer-nodemailer');\n        return { type: 'nodemailer', mailer: nodemailerMailer.default };\n      } catch (error) {\n        console.warn('Nodemailer not available, falling back to resend');\n        const resendMailer = await import('./mailer-resend');\n        return { type: 'resend', mailer: resendMailer.default };\n      }\n      \n    case 'none':\n    default:\n      console.warn('No mailer configuration found, using Resend as default. Please set RESEND_API_KEY or SMTP configuration.');\n      try {\n        const resendMailer = await import('./mailer-resend');\n        return { type: 'resend', mailer: resendMailer.default };\n      } catch (error) {\n        throw new Error('No mailer available. Please install and configure either Resend or Nodemailer.');\n      }\n  }\n}\n\n/**\n * Send an email using the detected mailer\n */\nexport async function sendEmail(options: EmailOptions): Promise<MailerResult> {\n  try {\n    const { mailer } = await getMailer();\n    return await mailer.sendEmail(options);\n  } catch (error) {\n    console.error('Failed to send email:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Send a welcome email to waitlist subscribers\n */\nexport async function sendWaitlistWelcomeEmail(\n  email: string, \n  name?: string, \n  position?: number\n): Promise<MailerResult> {\n  try {\n    const { mailer } = await getMailer();\n    return await mailer.sendWaitlistWelcomeEmail(email, name, position);\n  } catch (error) {\n    console.error('Failed to send waitlist welcome email:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Send waitlist approval email\n */\nexport async function sendWaitlistApprovalEmail(\n  email: string, \n  name?: string\n): Promise<MailerResult> {\n  try {\n    const { mailer } = await getMailer();\n    return await mailer.sendWaitlistApprovalEmail(email, name);\n  } catch (error) {\n    console.error('Failed to send waitlist approval email:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Test email configuration\n */\nexport async function testEmailConfiguration(): Promise<MailerResult> {\n  try {\n    const { mailer } = await getMailer();\n    return await mailer.testEmailConfiguration();\n  } catch (error) {\n    console.error('Failed to test email configuration:', error);\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Unknown error occurred'\n    };\n  }\n}\n\n/**\n * Get mailer configuration info\n */\nexport function getMailerInfo(): { \n  type: 'resend' | 'nodemailer' | 'none';\n  configured: boolean;\n  details: string;\n} {\n  const type = detectMailer();\n  \n  switch (type) {\n    case 'resend':\n      return {\n        type: 'resend',\n        configured: true,\n        details: 'Using Resend API for email delivery'\n      };\n      \n    case 'nodemailer':\n      const provider = process.env.EMAIL_PROVIDER || 'smtp';\n      return {\n        type: 'nodemailer',\n        configured: true,\n        details: `Using Nodemailer with ${provider.toUpperCase()} configuration`\n      };\n      \n    case 'none':\n    default:\n      return {\n        type: 'none',\n        configured: false,\n        details: 'No email configuration found. Please set RESEND_API_KEY or SMTP configuration.'\n      };\n  }\n}\n\n/**\n * Send a test email to verify configuration\n */\nexport async function sendTestEmail(to: string): Promise<MailerResult> {\n  const subject = 'Supreme Toolkit - Email Configuration Test';\n  const html = `\n    <h2>Email Configuration Test</h2>\n    <p>This is a test email to verify your Supreme Toolkit email configuration.</p>\n    <p><strong>Mailer:</strong> ${getMailerInfo().details}</p>\n    <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>\n    <p>If you received this email, your configuration is working correctly! 🎉</p>\n  `;\n  \n  const text = `\n    Email Configuration Test\n    \n    This is a test email to verify your Supreme Toolkit email configuration.\n    \n    Mailer: ${getMailerInfo().details}\n    Timestamp: ${new Date().toISOString()}\n    \n    If you received this email, your configuration is working correctly!\n  `;\n\n  return await sendEmail({\n    to,\n    subject,\n    html,\n    text,\n  });\n}\n\nexport default {\n  sendEmail,\n  sendWaitlistWelcomeEmail,\n  sendWaitlistApprovalEmail,\n  testEmailConfiguration,\n  getMailerInfo,\n  sendTestEmail,\n};",
      "type": "registry:lib",
      "target": "lib/mailer.ts"
    },
    {
      "name": "mailer-resend.ts",
      "content": "/**\n * Supreme Toolkit - Resend Mailer\n * \n * This module provides email functionality using Resend.\n * Resend is a modern email API that's easy to use and reliable.\n */\n\nimport { Resend } from 'resend';\n\n// Initialize Resend client\nconst resend = new Resend(process.env.RESEND_API_KEY);\n\nexport interface EmailOptions {\n  to: string | string[];\n  from?: string;\n  subject: string;\n  html?: string;\n  text?: string;\n  replyTo?: string;\n  cc?: string | string[];\n  bcc?: string | string[];\n  attachments?: Array<{\n    filename: string;\n    content: string | Buffer;\n    contentType?: string;\n  }>;\n}\n\n/**\n * Send an email using Resend\n */\nexport async function sendEmail(options: EmailOptions): Promise<{ success: boolean; id?: string; error?: string }> {\n  try {\n    if (!process.env.RESEND_API_KEY) {\n      throw new Error('RESEND_API_KEY environment variable is not set');\n    }\n\n    const fromEmail = options.from || process.env.RESEND_FROM_EMAIL || '<EMAIL>';\n\n    const emailData: any = {\n      from: fromEmail,\n      to: Array.isArray(options.to) ? options.to : [options.to],\n      subject: options.subject,\n      text: options.text || options.html || 'No content provided',\n    };\n\n    if (options.html) emailData.html = options.html;\n    if (options.replyTo) emailData.replyTo = options.replyTo;\n    if (options.cc) emailData.cc = options.cc;\n    if (options.bcc) emailData.bcc = options.bcc;\n    if (options.attachments) emailData.attachments = options.attachments;\n\n    const result = await resend.emails.send(emailData);\n\n    if (result.error) {\n      console.error('Resend error:', result.error);\n      return { success: false, error: result.error.message };\n    }\n\n    return { success: true, id: result.data?.id };\n  } catch (error) {\n    console.error('Email sending failed:', error);\n    return { \n      success: false, \n      error: error instanceof Error ? error.message : 'Unknown error occurred' \n    };\n  }\n}\n\n/**\n * Send a welcome email to waitlist subscribers\n */\nexport async function sendWaitlistWelcomeEmail(\n  email: string, \n  name?: string, \n  position?: number\n): Promise<{ success: boolean; error?: string }> {\n  const subject = 'Welcome to our waitlist! 🎉';\n  \n  const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>Welcome to our waitlist</title>\n      <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .content { background: #f9f9f9; padding: 30px; border-radius: 8px; }\n        .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }\n        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>Welcome to our waitlist!</h1>\n        </div>\n        \n        <div class=\"content\">\n          <h2>Hi ${name || 'there'}! 👋</h2>\n          \n          <p>Thank you for joining our waitlist! We're excited to have you on board.</p>\n          \n          ${position ? `<p><strong>Your position:</strong> #${position}</p>` : ''}\n          \n          <p>We'll keep you updated on our progress and let you know as soon as we're ready to welcome you.</p>\n          \n          <p>Thanks again for your interest!</p>\n          \n          <p>Best regards,<br>The Team</p>\n        </div>\n        \n        <div class=\"footer\">\n          <p>You're receiving this email because you signed up for our waitlist.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n\n  const text = `\n    Welcome to our waitlist!\n    \n    Hi ${name || 'there'},\n    \n    Thank you for joining our waitlist! We're excited to have you on board.\n    \n    ${position ? `Your position: #${position}` : ''}\n    \n    We'll keep you updated on our progress and let you know as soon as we're ready to welcome you.\n    \n    Thanks again for your interest!\n    The Team\n  `;\n\n  return await sendEmail({\n    to: email,\n    subject,\n    html,\n    text,\n  });\n}\n\n/**\n * Send waitlist approval email\n */\nexport async function sendWaitlistApprovalEmail(\n  email: string, \n  name?: string\n): Promise<{ success: boolean; error?: string }> {\n  const subject = 'You\\'re approved! Welcome aboard! 🚀';\n  \n  const html = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <meta charset=\"utf-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>You're approved!</title>\n      <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .content { background: #f0f8ff; padding: 30px; border-radius: 8px; border: 2px solid #007bff; }\n        .button { display: inline-block; background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; }\n        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }\n      </style>\n    </head>\n    <body>\n      <div class=\"container\">\n        <div class=\"header\">\n          <h1>🎉 You're approved! 🎉</h1>\n        </div>\n        \n        <div class=\"content\">\n          <h2>Hi ${name || 'there'}!</h2>\n          \n          <p><strong>Great news!</strong> You've been approved and can now access our platform.</p>\n          \n          <p>We're thrilled to welcome you aboard and can't wait to see what you'll create.</p>\n          \n          <div style=\"text-align: center;\">\n            <a href=\"${process.env.NEXT_PUBLIC_APP_URL || 'https://yourapp.com'}\" class=\"button\">Get Started Now</a>\n          </div>\n          \n          <p>If you have any questions or need help getting started, don't hesitate to reach out.</p>\n          \n          <p>Welcome to the community!</p>\n          \n          <p>Best regards,<br>The Team</p>\n        </div>\n        \n        <div class=\"footer\">\n          <p>You're receiving this email because you were on our waitlist.</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n\n  const text = `\n    You're approved!\n    \n    Hi ${name || 'there'},\n    \n    Great news! You've been approved and can now access our platform.\n    \n    We're thrilled to welcome you aboard and can't wait to see what you'll create.\n    \n    Get started: ${process.env.NEXT_PUBLIC_APP_URL || 'https://yourapp.com'}\n    \n    If you have any questions or need help getting started, don't hesitate to reach out.\n    \n    Welcome to the community!\n    The Team\n  `;\n\n  return await sendEmail({\n    to: email,\n    subject,\n    html,\n    text,\n  });\n}\n\n/**\n * Test email configuration\n */\nexport async function testEmailConfiguration(): Promise<{ success: boolean; error?: string }> {\n  try {\n    if (!process.env.RESEND_API_KEY) {\n      return { success: false, error: 'RESEND_API_KEY environment variable is not set' };\n    }\n\n    // Test with a simple email\n    const result = await sendEmail({\n      to: '<EMAIL>',\n      subject: 'Test Email Configuration',\n      text: 'This is a test email to verify Resend configuration.',\n      html: '<p>This is a test email to verify Resend configuration.</p>',\n    });\n\n    return result;\n  } catch (error) {\n    return { \n      success: false, \n      error: error instanceof Error ? error.message : 'Unknown error occurred' \n    };\n  }\n}\n\nexport default {\n  sendEmail,\n  sendWaitlistWelcomeEmail,\n  sendWaitlistApprovalEmail,\n  testEmailConfiguration,\n};",
      "type": "registry:lib",
      "target": "lib/mailer-resend.ts"
    }
