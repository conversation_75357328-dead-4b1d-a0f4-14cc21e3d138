#!/usr/bin/env tsx

/**
 * Test script for Supreme Toolkit module installation
 * 
 * This script tests the installation of modules using our CLI utilities.
 */

import { installModule, listModules, getModuleInfo, validateProject } from '../lib/cli';

async function main() {
  console.log('🚀 Supreme Toolkit - Module Installation Test\n');

  // Validate project structure
  console.log('📋 Validating project structure...');
  const validation = await validateProject();
  
  if (!validation.valid) {
    console.error('❌ Project validation failed:');
    validation.issues.forEach(issue => console.error(`  - ${issue}`));
    process.exit(1);
  }
  
  console.log('✅ Project structure is valid\n');

  // List available modules
  console.log('📦 Available modules:');
  const modules = await listModules();
  
  if (modules.length === 0) {
    console.log('  No modules found in registry');
    return;
  }
  
  modules.forEach(module => console.log(`  - ${module}`));
  console.log('');

  // Get module info
  const moduleName = 'waitlist-component';
  console.log(`ℹ️  Getting info for module: ${moduleName}`);
  const moduleInfo = await getModuleInfo(moduleName);
  
  if (!moduleInfo) {
    console.error(`❌ Module "${moduleName}" not found`);
    return;
  }
  
  console.log(`  Name: ${moduleInfo.name}`);
  console.log(`  Description: ${moduleInfo.meta.description}`);
  console.log(`  Version: ${moduleInfo.meta.version}`);
  console.log(`  Files: ${moduleInfo.files.length}`);
  console.log(`  Dependencies: ${moduleInfo.dependencies.length}`);
  console.log('');

  // Test installation (dry run)
  console.log(`🧪 Testing installation (dry run): ${moduleName}`);
  const dryRunResult = await installModule(moduleName, {
    dryRun: true,
    verbose: true,
  });
  
  if (!dryRunResult.success) {
    console.error('❌ Dry run failed:');
    dryRunResult.errors.forEach(error => console.error(`  - ${error}`));
    return;
  }
  
  console.log('✅ Dry run successful');
  console.log(`  Files that would be created: ${dryRunResult.filesCreated.length}`);
  dryRunResult.filesCreated.forEach(file => console.log(`    - ${file}`));
  
  if (dryRunResult.filesSkipped.length > 0) {
    console.log(`  Files that would be skipped: ${dryRunResult.filesSkipped.length}`);
    dryRunResult.filesSkipped.forEach(file => console.log(`    - ${file}`));
  }
  
  if (dryRunResult.warnings.length > 0) {
    console.log('  Warnings:');
    dryRunResult.warnings.forEach(warning => console.log(`    - ${warning}`));
  }
  
  console.log('');

  // Ask user if they want to proceed with actual installation
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const answer = await new Promise<string>((resolve) => {
    rl.question('Do you want to proceed with the actual installation? (y/N): ', resolve);
  });
  
  rl.close();

  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log('Installation cancelled.');
    return;
  }

  // Actual installation
  console.log(`📦 Installing module: ${moduleName}`);
  const installResult = await installModule(moduleName, {
    verbose: true,
    force: false,
  });
  
  if (!installResult.success) {
    console.error('❌ Installation failed:');
    installResult.errors.forEach(error => console.error(`  - ${error}`));
    return;
  }
  
  console.log('✅ Installation successful!');
  console.log(`  Files created: ${installResult.filesCreated.length}`);
  installResult.filesCreated.forEach(file => console.log(`    - ${file}`));
  
  if (installResult.filesSkipped.length > 0) {
    console.log(`  Files skipped: ${installResult.filesSkipped.length}`);
    installResult.filesSkipped.forEach(file => console.log(`    - ${file}`));
  }
  
  if (installResult.warnings.length > 0) {
    console.log('  Warnings:');
    installResult.warnings.forEach(warning => console.log(`    - ${warning}`));
  }

  console.log('\n🎉 Module installation completed!');
  console.log('\nNext steps:');
  console.log('1. Configure the module in config.tsx');
  console.log('2. Customize server actions in actions/waitlist-actions.ts');
  console.log('3. Add the WaitlistForm component to your pages');
  console.log('4. Test the functionality');
}

// Run the test
main().catch(console.error);
